import { ColumnDef } from "@tanstack/react-table";
import { formatDuration, intervalToDuration } from "date-fns";
import { VESTING_FREQUENCY_LABELS } from "~/lib/enum-labels";
import { RouterOutputs } from "~/trpc/react";

// Define the type for a single allocation row based on your router output
type Allocation = RouterOutputs["allocation"]["getAll"][number];
type IndividualAllocation = Allocation["allocations"][number];

export const getAllocationTableColumns = (): ColumnDef<Allocation>[] => [
  {
    accessorKey: "stakeholder_type",
    enableSorting: false,
    header: "User Type",
    enableHiding: false,
    meta: {
      columnTitle: "User Type",
    },
    filterFn: (row, id, filterValue: string[]) => {
      if (!filterValue?.length) return true;
      return filterValue.includes(row.getValue(id));
    },
  },
  {
    accessorKey: "group",
    enableSorting: false,
    header: "Group",
    enableHiding: false,
    meta: {
      columnTitle: "Group",
    },
    filterFn: (row, id, filterValue: string[]) => {
      if (!filterValue?.length) return true;
      return filterValue.includes(row.getValue(id));
    },
  },
  {
    id: "project.industry",
    header: "Industry",
    enableHiding: false,
    meta: {
      columnTitle: "Industry",
    },
    filterFn: (row, id, filterValue: string[]) => {
      if (!filterValue?.length) return true;
      const individualAllocations = row.original
        .allocations as IndividualAllocation[];
      if (!individualAllocations || individualAllocations.length === 0) {
        return false;
      }
      return individualAllocations.some((alloc) =>
        filterValue.includes(alloc.project?.industry ?? "")
      );
    },
    cell: () => null, // Cell displays nothing as it's just for filtering now
  },
  {
    accessorKey: "title",
    enableSorting: false,
    header: "Title",
    enableHiding: false,
    meta: {
      columnTitle: "Title",
    },
  },
  {
    id: "token_percentage",
    // Add a dummy accessorFn to potentially help enable sorting UI
    accessorFn: () => null,
    header: "Token Percentage",
    enableHiding: false,
    enableSorting: true,
    meta: {
      columnTitle: "Token percentage",
    },
    cell: ({ row, table }) => {
      const allAllocations = row.original.allocations as IndividualAllocation[];
      // Get active industry filter values from table state
      const columnFilters = table.getState().columnFilters;
      const industryFilter = columnFilters.find(
        (f) => f.id === "project.industry"
      );
      const activeIndustries =
        (industryFilter?.value as string[] | undefined) ?? [];

      // Filter allocations based on selected industries if any are selected
      const relevantAllocations =
        activeIndustries.length > 0
          ? allAllocations.filter((alloc) =>
              activeIndustries.includes(alloc.project?.industry ?? "")
            )
          : allAllocations;

      const percentages = relevantAllocations
        .map((alloc) => Number(alloc.totalTokenDecimal) * 100)
        .filter((p) => !Number.isNaN(p));

      if (percentages.length === 0) {
        return <div className="px-4 font-medium">-</div>;
      }

      const min = Math.min(...percentages);
      const max = Math.max(...percentages);

      const displayValue =
        min === max
          ? `${min.toLocaleString(undefined, { maximumSignificantDigits: 3 })}%`
          : `${min.toLocaleString(undefined, { maximumSignificantDigits: 3 })}% - ${max.toLocaleString(undefined, { maximumSignificantDigits: 3 })}%`;

      return <div className="px-4 font-medium">{displayValue}</div>;
    },
    sortingFn: (rowA, rowB, _columnId) => {
      const getMinValue = (row: typeof rowA) => {
        const allAllocations = row.original
          .allocations as IndividualAllocation[];
        const relevantAllocations = allAllocations; // Simplified for sorting - consider filter logic if needed
        const percentages = relevantAllocations
          .map((alloc) => Number(alloc.totalTokenDecimal) * 100)
          .filter((p) => !Number.isNaN(p));
        return percentages.length > 0 ? Math.min(...percentages) : -Infinity;
      };
      const valueA = getMinValue(rowA);
      const valueB = getMinValue(rowB);
      return valueA - valueB;
    },
  },
  {
    id: "token_amount_range",
    // Add a dummy accessorFn to potentially help enable sorting UI
    accessorFn: () => null,
    header: "Token Amount Range",
    enableHiding: true,
    enableSorting: true,
    meta: {
      columnTitle: "Token Amount Range",
    },
    cell: ({ row, table }) => {
      const allAllocations = row.original.allocations as IndividualAllocation[];

      // Get active industry filter values from table state
      const columnFilters = table.getState().columnFilters;
      const industryFilter = columnFilters.find(
        (f) => f.id === "project.industry"
      );
      const activeIndustries =
        (industryFilter?.value as string[] | undefined) ?? [];

      // Filter allocations based on selected industries if any are selected
      const relevantAllocations =
        activeIndustries.length > 0
          ? allAllocations.filter((alloc) =>
              activeIndustries.includes(alloc.project?.industry ?? "")
            )
          : allAllocations;

      const tokenAmounts = relevantAllocations
        .map((alloc) => Number(alloc.tokenAmount))
        .filter((amount) => !Number.isNaN(amount) && amount > 0);

      if (tokenAmounts.length === 0) {
        return <div className="px-4 font-medium">-</div>;
      }

      const min = Math.min(...tokenAmounts);
      const max = Math.max(...tokenAmounts);

      const displayValue =
        min === max
          ? `${min.toLocaleString(undefined, { maximumSignificantDigits: 6 })}`
          : `${min.toLocaleString(undefined, { maximumSignificantDigits: 6 })} - ${max.toLocaleString(undefined, { maximumSignificantDigits: 6 })}`;

      return <div className="px-4 font-medium">{displayValue}</div>;
    },
    sortingFn: (rowA, rowB, _columnId) => {
      const getMinValue = (row: typeof rowA) => {
        const allAllocations = row.original
          .allocations as IndividualAllocation[];
        const relevantAllocations = allAllocations; // Simplified for sorting - consider filter logic if needed
        const tokenAmounts = relevantAllocations
          .map((alloc) => Number(alloc.tokenAmount))
          .filter((amount) => !Number.isNaN(amount) && amount > 0);
        return tokenAmounts.length > 0 ? Math.min(...tokenAmounts) : -Infinity;
      };
      const valueA = getMinValue(rowA);
      const valueB = getMinValue(rowB);
      return valueA - valueB;
    },
  },
  {
    id: "equity_token_ratio",
    // Add a dummy accessorFn to potentially help enable sorting UI
    accessorFn: () => null,
    header: "Equity-Token Ratio",
    enableHiding: true,
    enableSorting: true,
    meta: {
      columnTitle: "Equity-Token Ratio",
    },
    cell: ({ row, table }) => {
      const allAllocations = row.original.allocations as IndividualAllocation[];

      // Get active industry filter values from table state
      const columnFilters = table.getState().columnFilters;
      const industryFilter = columnFilters.find(
        (f) => f.id === "project.industry"
      );
      const activeIndustries =
        (industryFilter?.value as string[] | undefined) ?? [];

      // Filter allocations based on selected industries if any are selected
      const relevantAllocations =
        activeIndustries.length > 0
          ? allAllocations.filter((alloc) =>
              activeIndustries.includes(alloc.project?.industry ?? "")
            )
          : allAllocations;

      const ratios = relevantAllocations
        .map((alloc) => {
          const equityDecimal = Number(alloc.equityDecimal);
          const tokenDecimal = Number(alloc.totalTokenDecimal);

          if (
            Number.isNaN(equityDecimal) ||
            Number.isNaN(tokenDecimal) ||
            equityDecimal === 0
          ) {
            return Number.NaN;
          }
          return tokenDecimal / equityDecimal;
        })
        .filter((r) => !Number.isNaN(r));

      if (ratios.length === 0) {
        return <div className="px-4 font-medium">-</div>;
      }

      const min = Math.min(...ratios);
      const max = Math.max(...ratios);

      const displayValue =
        min === max
          ? `${min.toFixed(2)}`
          : `${min.toFixed(2)} - ${max.toFixed(2)}`;

      return <div className="px-4 font-medium">{displayValue}</div>;
    },
    sortingFn: (rowA, rowB, _columnId) => {
      const getMinValue = (row: typeof rowA) => {
        const allAllocations = row.original
          .allocations as IndividualAllocation[];
        const relevantAllocations = allAllocations; // Simplified for sorting - consider filter logic if needed
        const ratios = relevantAllocations
          .map((alloc) => {
            const equityDecimal = Number(alloc.equityDecimal);
            const tokenDecimal = Number(alloc.totalTokenDecimal);
            if (
              Number.isNaN(equityDecimal) ||
              Number.isNaN(tokenDecimal) ||
              equityDecimal === 0
            ) {
              return Number.NaN;
            }
            return tokenDecimal / equityDecimal;
          })
          .filter((r) => !Number.isNaN(r));
        return ratios.length > 0 ? Math.min(...ratios) : -Infinity;
      };
      const valueA = getMinValue(rowA);
      const valueB = getMinValue(rowB);
      return valueA - valueB;
    },
  },
  {
    accessorKey: "lockingDuration",
    header: "Locking Duration",
    cell: ({ row }) => {
      const allocation = row.original.allocations.at(0);
      const vestingSchedule = allocation?.vestingSchedules?.[0];
      if (!vestingSchedule) {
        return <div className="px-4 font-medium">-</div>;
      }
      return (
        <div className="px-4 font-medium">{vestingSchedule.cliffDuration}</div>
      );
    },
    enableHiding: true,
    meta: {
      columnTitle: "Locking Duration",
    },
  },
  {
    accessorKey: "vestingDuration",
    header: "Vesting Duration",
    cell: ({ row }) => {
      const allocation = row.original.allocations.at(0);
      const vestingSchedule = allocation?.vestingSchedules?.[0];
      if (!vestingSchedule) {
        return <div className="px-4 font-medium">-</div>;
      }

      const startDate = vestingSchedule.vestingStartDate;
      const endDate = vestingSchedule.vestingEndDate;
      const duration = intervalToDuration({
        start: startDate,
        end: endDate,
      });

      return <div className="px-4 font-medium">{formatDuration(duration)}</div>;
    },
    enableHiding: true,
    meta: {
      columnTitle: "Vesting Duration",
    },
  },
  {
    accessorKey: "vestingType",
    header: "Vesting Type",
    cell: ({ row }) => {
      const allocation = row.original.allocations.at(0);
      const vestingSchedule = allocation?.vestingSchedules?.[0];
      if (!vestingSchedule) {
        return <div className="px-4 font-medium">-</div>;
      }
      return (
        <div className="px-4 capitalize">
          {VESTING_FREQUENCY_LABELS[vestingSchedule.vestingFrequency]}
        </div>
      );
    },
    enableHiding: true,
    meta: {
      columnTitle: "Vesting Type",
    },
  },
];
